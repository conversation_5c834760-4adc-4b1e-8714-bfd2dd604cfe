<template>
  <div class="p-4">
    <h2 class="text-xl mb-4">多列排序测试</h2>
    
    <div class="mb-4">
      <p class="text-sm text-gray-600 mb-2">
        默认排序规则：电压等级 > 总越限时间 > 差额最大值 > 最大潮流 > 名字首字母
      </p>
      <button 
        @click="resetData" 
        class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        重置测试数据
      </button>
    </div>
    
    <!-- 测试多列排序的表格 -->
    <DataTable
      :columns="columns"
      :data="testData"
      height="500px"
      :item-size="62"
      @sort="handleSort"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import DataTable from '../components/shared/DataTable.vue'

// 定义表格列 - 模拟断面统计表格的结构
const columns = ref([
  {
    key: 'name',
    title: '断面名称',
    sortable: true,
    align: 'left' as const,
    width: '20%',
    sorter: {
      compare: (a: any, b: any) => {
        const aStr = String(a.name || '')
        const bStr = String(b.name || '')
        return aStr.localeCompare(bStr, 'zh-CN', { numeric: true })
      },
      multiple: 1, // 最低优先级
    },
    defaultSortOrder: false as const,
  },
  {
    key: 'volt',
    title: '电压等级',
    sortable: true,
    align: 'center' as const,
    width: '10%',
    sorter: {
      compare: (a: any, b: any) => {
        const voltOrder: { [key: string]: number } = {
          '500KV': 3,
          '220KV': 2,
        }
        const aVolt = voltOrder[a.volt] || 1
        const bVolt = voltOrder[b.volt] || 1
        return bVolt - aVolt // 降序，高电压优先
      },
      multiple: 5, // 最高优先级
    },
    defaultSortOrder: false as const,
  },
  {
    key: 'maxValue',
    title: '最大潮流',
    sortable: true,
    align: 'center' as const,
    width: '15%',
    sorter: {
      compare: (a: any, b: any) => {
        const aNum = parseFloat(a.maxValue) || 0
        const bNum = parseFloat(b.maxValue) || 0
        return bNum - aNum // 降序
      },
      multiple: 2,
    },
    defaultSortOrder: false as const,
  },
  {
    key: 'maxDiffValue',
    title: '差额最大值',
    sortable: true,
    align: 'center' as const,
    width: '15%',
    sorter: {
      compare: (a: any, b: any) => {
        const aNum = parseFloat(a.maxDiffValue) || 0
        const bNum = parseFloat(b.maxDiffValue) || 0
        return bNum - aNum // 降序
      },
      multiple: 3,
    },
    defaultSortOrder: false as const,
  },
  {
    key: 'totalOverTime',
    title: '总越限时间',
    sortable: true,
    align: 'center' as const,
    width: '15%',
    sorter: {
      compare: (a: any, b: any) => {
        const aNum = parseFloat(a.totalOverTime) || 0
        const bNum = parseFloat(b.totalOverTime) || 0
        return bNum - aNum // 降序
      },
      multiple: 4,
    },
    defaultSortOrder: false as const,
  },
  {
    key: 'limit',
    title: '限额',
    sortable: true,
    align: 'center' as const,
    width: '15%',
  },
])

// 生成测试数据
const generateTestData = () => {
  const voltLevels = ['500KV', '220KV', '110KV']
  const sectionNames = ['断面A', '断面B', '断面C', '断面D', '断面E', '断面F']
  
  return Array.from({ length: 20 }, (_, i) => ({
    id: i + 1,
    name: sectionNames[i % sectionNames.length] + (Math.floor(i / sectionNames.length) + 1),
    volt: voltLevels[i % voltLevels.length],
    limit: (1000 + Math.floor(Math.random() * 2000)).toString(),
    maxValue: (800 + Math.floor(Math.random() * 1500)).toString(),
    maxDiffValue: (100 + Math.floor(Math.random() * 500)).toString(),
    totalOverTime: (10 + Math.floor(Math.random() * 200)).toString(),
    longestOverTime: (5 + Math.floor(Math.random() * 100)).toString(),
  }))
}

// 默认排序函数
const applyDefaultSort = (data: any[]) => {
  return data.sort((a, b) => {
    // 1. 电压等级排序（最高优先级）
    const voltOrder: { [key: string]: number } = {
      '500KV': 3,
      '220KV': 2,
    }
    const aVolt = voltOrder[a.volt] || 1
    const bVolt = voltOrder[b.volt] || 1
    if (aVolt !== bVolt) {
      return bVolt - aVolt // 降序，高电压优先
    }

    // 2. 总越限时间排序
    const aTotalOverTime = parseFloat(a.totalOverTime) || 0
    const bTotalOverTime = parseFloat(b.totalOverTime) || 0
    if (aTotalOverTime !== bTotalOverTime) {
      return bTotalOverTime - aTotalOverTime // 降序
    }

    // 3. 差额最大值排序
    const aMaxDiffValue = parseFloat(a.maxDiffValue) || 0
    const bMaxDiffValue = parseFloat(b.maxDiffValue) || 0
    if (aMaxDiffValue !== bMaxDiffValue) {
      return bMaxDiffValue - aMaxDiffValue // 降序
    }

    // 4. 最大潮流排序
    const aMaxValue = parseFloat(a.maxValue) || 0
    const bMaxValue = parseFloat(b.maxValue) || 0
    if (aMaxValue !== bMaxValue) {
      return bMaxValue - aMaxValue // 降序
    }

    // 5. 名字首字母排序（最低优先级）
    const aName = String(a.name || '')
    const bName = String(b.name || '')
    return aName.localeCompare(bName, 'zh-CN', { numeric: true }) // 升序
  })
}

// 创建测试数据并应用默认排序
const testData = ref(applyDefaultSort(generateTestData()))

// 重置数据
const resetData = () => {
  testData.value = applyDefaultSort(generateTestData())
}

// 处理排序
const handleSort = (column: any, order: 'asc' | 'desc' | null, multiSortState: any) => {
  console.log('多列排序测试:', column.key, order, multiSortState)
  
  if (!order && Object.keys(multiSortState).length === 0) {
    // 恢复默认排序
    resetData()
    return
  }

  // 如果有多列排序状态，使用多列排序
  if (Object.keys(multiSortState).length > 0) {
    // 按优先级排序多列排序状态
    const sortColumns = Object.entries(multiSortState)
      .map(([key, state]: [string, any]) => ({
        key,
        order: state.order,
        multiple: state.multiple,
        column: columns.value.find((col: any) => col.key === key),
      }))
      .sort((a, b) => b.multiple - a.multiple) // 按优先级降序排列

    testData.value.sort((a, b) => {
      for (const sortCol of sortColumns) {
        let result = 0

        // 如果列有自定义比较函数，使用它
        if (sortCol.column?.sorter && typeof sortCol.column.sorter === 'object' && sortCol.column.sorter.compare) {
          result = sortCol.column.sorter.compare(a, b)
        } else {
          // 默认排序逻辑
          const aValue = (a as any)[sortCol.key]
          const bValue = (b as any)[sortCol.key]

          // 处理数值类型的列
          if (['limit', 'maxValue', 'maxDiffValue', 'totalOverTime', 'longestOverTime'].includes(sortCol.key)) {
            const aNum = parseFloat(aValue) || 0
            const bNum = parseFloat(bValue) || 0
            result = aNum - bNum
          } else {
            // 处理字符串类型的列
            const aStr = String(aValue || '')
            const bStr = String(bValue || '')
            result = aStr.localeCompare(bStr, 'zh-CN', { numeric: true })
          }
        }

        // 根据排序方向调整结果
        if (sortCol.order === 'desc') {
          result = -result
        }

        // 如果当前列的比较结果不为0，返回结果
        if (result !== 0) {
          return result
        }
      }

      return 0 // 所有列都相等
    })
  }
}
</script>
