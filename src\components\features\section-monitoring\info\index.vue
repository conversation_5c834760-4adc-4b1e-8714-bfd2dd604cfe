<template>
  <div class="flex px-7.5 py-4 items-center bg-#F8F8F8 border-b-1 border-b-#D7D7D7 border-b-solid">
    <span class="text-2xl text-#9E9E9E mr-9">查询结果</span>

    <div class="flex flex-1">
      <InputGroup size="large" label="时间" class="mr-2.5 max-w-500px">
        <n-date-picker
          v-model:value="timeRange"
          class="w-full"
          type="datetimerange"
          size="large"
          placeholder="请选择时间"
        >
        </n-date-picker>
      </InputGroup>
    </div>
    <div class="flex">
      <n-button type="info" class="mr-2.5" size="large" @click="handleSearch" :loading="loading">
        <template #icon>
          <n-icon><SearchIcon /></n-icon>
        </template>
        搜索
      </n-button>
      <ExportButton
        :data="tableData"
        :columns="columns"
        filename="断面单日统计列表"
        button-class="mr-2.5"
        @export="handleExport"
      />

      <n-button type="info" size="large" @click="handleBack">
        <template #icon>
          <n-icon><ChevronLeft20FilledIcon /></n-icon>
        </template>
        返回
      </n-button>
    </div>
  </div>
  <LineChart title="断面潮流曲线" :data="multiSeriesData" height="450px" class="mt-2" />

  <SectionDetail :section-data="sectionDetailData"></SectionDetail>
  <DataTable
    :columns="columns"
    :data="tableData"
    :loading="loading"
    height="calc(100vh - 740px)"
    @sort="handleSort"
  >
  </DataTable>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { NDatePicker, NButton, NIcon } from 'naive-ui'
import InputGroup from '@/components/shared/InputGroup.vue'

import { SearchIcon, ChevronLeft20FilledIcon } from '@/utils/constant/icons'

import LineChart from '@/components/shared/charts/LineChart.vue'
import DataTable from '@/components/shared/DataTable.vue'
import ExportButton from '@/components/shared/ExportButton.vue'
import SectionDetail from './SectionDetail.vue'
import { useSectionMonitoringStore } from '@/stores'
import {
  SectionMonitoringService,
  type SectionStatisticDetailResponse,
  type SectionStatisticDetailParams,
} from '@/utils/api'
import type { SeriesData } from '@/types/chart'
import { formatTime, getTodayTimeRange } from '@/utils/tools/'

const timestampRange = getTodayTimeRange()

// 响应式数据
const sectionMonitoringStore = useSectionMonitoringStore()
const loading = ref(false)
const sectionDetailData = ref<SectionStatisticDetailResponse | null>(null)

// 时间选择器数据
const timeRange = ref<[number, number]>([timestampRange[0], timestampRange[1]])

// 图表数据
const multiSeriesData = computed<SeriesData[]>(() => {
  if (!sectionDetailData.value?.dataList) {
    return []
  }

  const dataList = sectionDetailData.value.dataList

  return [
    {
      name: '潮流值',
      data: dataList.map((item) => ({
        name: item.time,
        value: parseFloat(item.value) || 0,
      })),
    },
    {
      name: '限额值',
      data: dataList.map((item) => ({
        name: item.time,
        value: parseFloat(item.limit) || 0,
      })),
      color: 'rgba(230, 176, 46, 1)',
      gradientColors: {
        start: 'rgba(230, 176, 46, 0)',
        end: 'rgba(230, 176, 46, 0)',
      },
    },
  ]
})

// 定义表格列配置
const columns = ref([
  {
    key: 'time',
    title: '日期',
    sortable: true,
    align: 'center' as const,
    width: '12%',
    sorter: {
      compare: (a: any, b: any) => {
        const aTime = new Date(a.time).getTime()
        const bTime = new Date(b.time).getTime()
        return aTime - bTime // 升序
      },
      multiple: 1, // 最低优先级
    },
    defaultSortOrder: false as const,
  },
  {
    key: 'overPeriod',
    title: '越限时间段',
    sortable: false,
    align: 'center' as const,
    width: '15%',
  },
  {
    key: 'limit',
    title: '限额',
    sortable: true,
    align: 'center' as const,
    width: '12%',
  },
  {
    key: 'maxValue',
    title: '最大潮流',
    sortable: true,
    align: 'center' as const,
    width: '12%',
    sorter: {
      compare: (a: any, b: any) => {
        const aNum = parseFloat(a.maxValue) || 0
        const bNum = parseFloat(b.maxValue) || 0
        return bNum - aNum // 降序
      },
      multiple: 2,
    },
    defaultSortOrder: false as const,
  },
  {
    key: 'maxDiffValue',
    title: '差额最大值',
    sortable: true,
    align: 'center' as const,
    width: '12%',
    sorter: {
      compare: (a: any, b: any) => {
        const aNum = parseFloat(a.maxDiffValue) || 0
        const bNum = parseFloat(b.maxDiffValue) || 0
        return bNum - aNum // 降序
      },
      multiple: 3,
    },
    defaultSortOrder: false as const,
  },
  {
    key: 'totalOverTime',
    title: '总越限时间',
    sortable: true,
    align: 'center' as const,
    width: '12%',
    sorter: {
      compare: (a: any, b: any) => {
        const aNum = parseFloat(a.totalOverTime) || 0
        const bNum = parseFloat(b.totalOverTime) || 0
        return bNum - aNum // 降序
      },
      multiple: 4,
    },
    defaultSortOrder: false as const,
  },
  {
    key: 'longestOverTime',
    title: '最长出现越限时长',
    sortable: true,
    align: 'center' as const,
    width: '15%',
  },
])

// 默认排序函数 - 按照 电压等级>总越限时间>差额最大值>最大潮流>名字首字母 的优先级排序
const applyDefaultSort = (data: any[]) => {
  return data.sort((a, b) => {
    // 由于详情页面的数据结构可能不同，这里需要根据实际字段进行排序
    // 如果有电压等级字段，按电压等级排序
    if (a.volt && b.volt) {
      const voltOrder: { [key: string]: number } = {
        '500KV': 3,
        '220KV': 2,
      }
      const aVolt = voltOrder[a.volt] || 1
      const bVolt = voltOrder[b.volt] || 1
      if (aVolt !== bVolt) {
        return bVolt - aVolt // 降序，高电压优先
      }
    }

    // 总越限时间排序
    if (a.totalOverTime && b.totalOverTime) {
      const aTotalOverTime = parseFloat(a.totalOverTime) || 0
      const bTotalOverTime = parseFloat(b.totalOverTime) || 0
      if (aTotalOverTime !== bTotalOverTime) {
        return bTotalOverTime - aTotalOverTime // 降序
      }
    }

    // 差额最大值排序
    if (a.maxDiffValue && b.maxDiffValue) {
      const aMaxDiffValue = parseFloat(a.maxDiffValue) || 0
      const bMaxDiffValue = parseFloat(b.maxDiffValue) || 0
      if (aMaxDiffValue !== bMaxDiffValue) {
        return bMaxDiffValue - aMaxDiffValue // 降序
      }
    }

    // 最大潮流排序
    if (a.maxValue && b.maxValue) {
      const aMaxValue = parseFloat(a.maxValue) || 0
      const bMaxValue = parseFloat(b.maxValue) || 0
      if (aMaxValue !== bMaxValue) {
        return bMaxValue - aMaxValue // 降序
      }
    }

    // 时间排序（详情页面特有）
    if (a.time && b.time) {
      return new Date(a.time).getTime() - new Date(b.time).getTime() // 升序
    }

    return 0
  })
}

// 表格数据
const tableData = computed(() => {
  const rawData = sectionDetailData.value?.statisticDataList || []
  // 应用默认排序
  return applyDefaultSort([...rawData])
})

// 获取断面统计详情数据
const fetchSectionStatisticDetail = async () => {
  if (!sectionMonitoringStore.selectedTableRow || !timeRange.value) {
    return
  }

  loading.value = true
  try {
    const params: SectionStatisticDetailParams = {
      startTime: formatTime(timeRange.value[0]),
      endTime: formatTime(timeRange.value[1]),
      sectionId: sectionMonitoringStore.selectedTableRow.id,
    }

    const response = await SectionMonitoringService.getSectionStatisticDetail(params)
    sectionDetailData.value = response
  } catch (error) {
    console.error('获取断面统计详情失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索处理函数
const handleSearch = () => {
  fetchSectionStatisticDetail()
}

// 排序处理函数
const handleSort = (column: any, order: 'asc' | 'desc' | null, multiSortState: any) => {
  console.log('详情页排序:', column.key, order, multiSortState)
  // 详情页面的排序由computed属性中的默认排序处理
  // 这里可以添加额外的排序逻辑，如果需要的话
}

// 导出处理函数
const handleExport = (data: any[], columns: any[], filename: string, exportType: string) => {
  console.log('导出表格:', { data, columns, filename, exportType })
  // 导出逻辑已在ExportButton组件中实现
}

const handleBack = () => {
  sectionMonitoringStore.selectedTableRow = null
}

// 监听选中行变化，自动获取数据
watch(
  () => sectionMonitoringStore.selectedTableRow,
  (newRow) => {
    if (newRow && timeRange.value) {
      fetchSectionStatisticDetail()
    }
  },
  { immediate: true },
)

// 组件挂载时设置默认时间范围
onMounted(() => {
  // 设置默认时间范围为当前月份
  const now = new Date()
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
  const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59)
  timeRange.value = [startOfMonth.getTime(), endOfMonth.getTime()]

  // 如果有选中的行，则获取数据
  if (sectionMonitoringStore.selectedTableRow) {
    fetchSectionStatisticDetail()
  }
})
</script>
