/**
 * 导出工具类
 * 支持Excel、CSV、JSON等格式的数据导出
 */

export interface ExportColumn {
  key: string
  title: string
  width?: number
  formatter?: (value: any) => string
}

export interface ExportOptions {
  filename?: string
  sheetName?: string
  columns?: ExportColumn[]
  data: any[]
  exportType?: 'excel' | 'csv' | 'json'
}

/**
 * 导出为CSV格式
 */
export const exportToCSV = (options: ExportOptions) => {
  const { data, columns, filename = 'export_data' } = options
  
  if (!data || data.length === 0) {
    throw new Error('没有数据可导出')
  }

  // 如果没有指定列，则使用数据的所有键
  const exportColumns = columns || Object.keys(data[0]).map(key => ({
    key,
    title: key,
  }))

  // 生成CSV内容
  const headers = exportColumns.map(col => col.title).join(',')
  const rows = data.map(row => 
    exportColumns.map(col => {
      let value = row[col.key] || ''
      
      // 如果有格式化函数，使用格式化函数
      if (col.formatter) {
        value = col.formatter(value)
      }
      
      // 处理包含逗号、换行符或双引号的值
      if (typeof value === 'string' && (value.includes(',') || value.includes('\n') || value.includes('"'))) {
        value = `"${value.replace(/"/g, '""')}"`
      }
      
      return value
    }).join(',')
  )

  const csvContent = [headers, ...rows].join('\n')
  
  // 添加BOM以支持中文
  const BOM = '\uFEFF'
  downloadFile(BOM + csvContent, `${filename}.csv`, 'text/csv;charset=utf-8')
}

/**
 * 导出为JSON格式
 */
export const exportToJSON = (options: ExportOptions) => {
  const { data, filename = 'export_data' } = options
  
  if (!data || data.length === 0) {
    throw new Error('没有数据可导出')
  }

  const jsonContent = JSON.stringify(data, null, 2)
  downloadFile(jsonContent, `${filename}.json`, 'application/json')
}

/**
 * 导出为Excel格式（简化版，使用HTML表格）
 * 注意：这是一个简化的Excel导出，实际项目中建议使用 xlsx 库
 */
export const exportToExcel = (options: ExportOptions) => {
  const { data, columns, filename = 'export_data', sheetName = 'Sheet1' } = options
  
  if (!data || data.length === 0) {
    throw new Error('没有数据可导出')
  }

  // 如果没有指定列，则使用数据的所有键
  const exportColumns = columns || Object.keys(data[0]).map(key => ({
    key,
    title: key,
  }))

  // 生成HTML表格
  let html = `
    <html xmlns:o="urn:schemas-microsoft-com:office:office" 
          xmlns:x="urn:schemas-microsoft-com:office:excel" 
          xmlns="http://www.w3.org/TR/REC-html40">
    <head>
      <meta charset="utf-8">
      <meta name="ProgId" content="Excel.Sheet">
      <meta name="Generator" content="Microsoft Excel 11">
      <style>
        table { border-collapse: collapse; }
        th, td { border: 1px solid #ccc; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
      </style>
    </head>
    <body>
      <table>
        <thead>
          <tr>
  `

  // 添加表头
  exportColumns.forEach(col => {
    html += `<th>${col.title}</th>`
  })
  html += '</tr></thead><tbody>'

  // 添加数据行
  data.forEach(row => {
    html += '<tr>'
    exportColumns.forEach(col => {
      let value = row[col.key] || ''
      
      // 如果有格式化函数，使用格式化函数
      if (col.formatter) {
        value = col.formatter(value)
      }
      
      html += `<td>${value}</td>`
    })
    html += '</tr>'
  })

  html += '</tbody></table></body></html>'

  // 下载文件
  downloadFile(html, `${filename}.xls`, 'application/vnd.ms-excel')
}

/**
 * 下载文件
 */
const downloadFile = (content: string, filename: string, mimeType: string) => {
  const blob = new Blob([content], { type: mimeType })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  link.style.display = 'none'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

/**
 * 通用导出函数
 */
export const exportData = (options: ExportOptions) => {
  const { exportType = 'excel' } = options
  
  switch (exportType) {
    case 'csv':
      return exportToCSV(options)
    case 'json':
      return exportToJSON(options)
    case 'excel':
    default:
      return exportToExcel(options)
  }
}

/**
 * 格式化常用数据类型
 */
export const formatters = {
  // 日期格式化
  date: (value: any) => {
    if (!value) return ''
    const date = new Date(value)
    return date.toLocaleDateString('zh-CN')
  },
  
  // 日期时间格式化
  datetime: (value: any) => {
    if (!value) return ''
    const date = new Date(value)
    return date.toLocaleString('zh-CN')
  },
  
  // 数字格式化（保留两位小数）
  number: (value: any) => {
    if (value === null || value === undefined || value === '') return ''
    const num = parseFloat(value)
    return isNaN(num) ? value : num.toFixed(2)
  },
  
  // 货币格式化
  currency: (value: any) => {
    if (value === null || value === undefined || value === '') return ''
    const num = parseFloat(value)
    return isNaN(num) ? value : `¥${num.toFixed(2)}`
  },
}
