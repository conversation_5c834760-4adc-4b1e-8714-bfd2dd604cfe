<template>
  <SearchForm :loading="loading" @search="handleSearch" @reset="handleReset">
    <template #form-fields>
      <!-- <InputGroup size="large" label="开始时间" class="mr-2.5 max-w-317px">
        <n-date-picker
          class="w-full"
          v-model:value="formData.startTime"
          type="datetime"
          size="large"
          placeholder="请选择开始时间"
        >
        </n-date-picker>
      </InputGroup>

      <InputGroup size="large" label="结束时间" class="mr-2.5 max-w-317px">
        <n-date-picker
          class="w-full"
          v-model:value="formData.endTime"
          type="datetime"
          size="large"
          placeholder="请选择结束时间"
        >
        </n-date-picker>
      </InputGroup> -->

      <InputGroup size="large" label="时间" class="mr-2.5 max-w-500px">
        <n-date-picker
          v-model:value="formData.timeRange"
          class="w-full"
          type="datetimerange"
          size="large"
          placeholder="请选择时间"
        >
        </n-date-picker>
      </InputGroup>

      <InputGroup size="large" label="名称" class="mr-2.5 max-w-317px">
        <n-input v-model:value="formData.name" size="large" placeholder="请输入名称" clearable>
        </n-input>
      </InputGroup>

      <InputGroup size="large" label="电压" class="mr-2.5 max-w-212px">
        <n-select
          v-model:value="formData.volt"
          class="w-full"
          placeholder=""
          :options="voltOptions"
          size="large"
          clearable
        />
      </InputGroup>

      <InputGroup size="large" label="状态" class="mr-2.5 max-w-212px">
        <n-select
          v-model:value="formData.status"
          size="large"
          placeholder=""
          :options="statusOptions"
          clearable
        />
      </InputGroup>
    </template>

    <template #action-buttons>
      <ExportButton
        :data="tableData"
        :columns="columnsByExport"
        filename="断面统计列表"
        @export="handleExport"
      />
    </template>
  </SearchForm>

  <DataTable
    :columns="columns"
    :data="tableData"
    :loading="loading"
    height="calc(100vh - 170px)"
    @sort="handleSort"
  >
    <template #action="{ item, index }">
      <n-button type="primary" @click="handleViewDetail(item as TableRowData, index)">
        查看
      </n-button>
    </template>
  </DataTable>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { NDatePicker, NInput, NButton, NSelect, type SelectOption } from 'naive-ui'
import DataTable from '@/components/shared/DataTable.vue'
import InputGroup from '@/components/shared/InputGroup.vue'
import SearchForm from '@/components/shared/SearchForm.vue'
import ExportButton from '@/components/shared/ExportButton.vue'
import {
  SectionMonitoringService,
  type SectionStatisticData,
  type SectionStatisticQueryParams,
} from '@/utils/api'

import { useSectionMonitoringStore } from '@/stores/'
import { formatTime, getTodayTimeRange } from '@/utils/tools/'

// 定义数据类型
interface TableRowData {
  id: string
  name: string
  volt: string
  limit: string
  maxValue: string
  maxDiffValue: string
  totalOverTime: string
  longestOverTime: string
  maxValueTime: string
}

// 定义表格列配置
const columns = [
  {
    key: 'name',
    title: '断面名称',
    align: 'left' as const,
    width: '20%',
  },
  {
    key: 'volt',
    title: '电压等级',
    align: 'center' as const,
    width: '10%',
  },
  {
    key: 'limit',
    title: '限额',
    align: 'center' as const,
    width: '10%',
  },
  {
    key: 'maxValue',
    title: '最大潮流',
    sortable: true,
    align: 'center' as const,
    width: '10%',
    sorter: {
      compare: (a: any, b: any) => {
        const aNum = parseFloat(a.maxValue) || 0
        const bNum = parseFloat(b.maxValue) || 0
        return bNum - aNum // 降序
      },
      multiple: 2,
    },
  },
  {
    key: 'maxDiffValue',
    title: '差额最大值',
    sortable: true,
    align: 'center' as const,
    width: '10%',
    sorter: {
      compare: (a: any, b: any) => {
        const aNum = parseFloat(a.maxDiffValue) || 0
        const bNum = parseFloat(b.maxDiffValue) || 0
        return bNum - aNum // 降序
      },
      multiple: 3,
    },
  },
  {
    key: 'totalOverTime',
    title: '总越限时间',
    sortable: true,
    align: 'center' as const,
    width: '10%',
    sorter: {
      compare: (a: any, b: any) => {
        const aNum = parseFloat(a.totalOverTime) || 0
        const bNum = parseFloat(b.totalOverTime) || 0
        return bNum - aNum // 降序
      },
      multiple: 4,
    },
  },
  {
    key: 'longestOverTime',
    title: '最长出现越线时长',
    sortable: true,
    align: 'center' as const,
    width: '12%',
  },
  {
    key: 'maxValueTime',
    title: '最大潮流越线出现时间',
    sortable: false,
    align: 'center' as const,
    width: '13%',
  },
  {
    key: 'action',
    title: '详情',
    sortable: false,
    width: '5%',
    align: 'center' as const,
  },
]

// 定义表格数据
const tableData = ref<TableRowData[]>([])

// 定义加载状态
const loading = ref(false)

// 定义响应式变量
const sectionMonitoringStore = useSectionMonitoringStore()

const timestampRange = getTodayTimeRange()

// 表单数据
const formData = ref<{
  timeRange: [number, number]
  name: string
  volt: string | null
  status: string | null
}>({
  timeRange: [timestampRange[0], timestampRange[1]],
  name: '',
  volt: null,
  status: null,
})

// 选项配置
const voltOptions: SelectOption[] = [
  {
    label: '500KV',
    value: '500KV',
  },
  {
    label: '220KV',
    value: '220KV',
  },
]

const statusOptions: SelectOption[] = [
  {
    label: '越限',
    value: '越限',
  },
  {
    label: '重载',
    value: '重载',
  },
]

// 默认排序函数 - 按照 电压等级>总越限时间>差额最大值>最大潮流>名字首字母 的优先级排序
const applyDefaultSort = (data: TableRowData[]) => {
  return data.sort((a, b) => {
    // 1. 电压等级排序（最高优先级）
    const voltOrder: { [key: string]: number } = {
      '500KV': 3,
      '220KV': 2,
    }
    const aVolt = voltOrder[a.volt] || 1
    const bVolt = voltOrder[b.volt] || 1
    if (aVolt !== bVolt) {
      return bVolt - aVolt // 降序，高电压优先
    }

    // 2. 总越限时间排序
    const aTotalOverTime = parseFloat(a.totalOverTime) || 0
    const bTotalOverTime = parseFloat(b.totalOverTime) || 0
    if (aTotalOverTime !== bTotalOverTime) {
      return bTotalOverTime - aTotalOverTime // 降序
    }

    // 3. 差额最大值排序
    const aMaxDiffValue = parseFloat(a.maxDiffValue) || 0
    const bMaxDiffValue = parseFloat(b.maxDiffValue) || 0
    if (aMaxDiffValue !== bMaxDiffValue) {
      return bMaxDiffValue - aMaxDiffValue // 降序
    }

    // 4. 最大潮流排序
    const aMaxValue = parseFloat(a.maxValue) || 0
    const bMaxValue = parseFloat(b.maxValue) || 0
    if (aMaxValue !== bMaxValue) {
      return bMaxValue - aMaxValue // 降序
    }

    // 5. 名字首字母排序（最低优先级）
    const aName = String(a.name || '')
    const bName = String(b.name || '')
    return aName.localeCompare(bName, 'zh-CN', { numeric: true }) // 升序
  })
}

// 获取断面统计数据
const fetchSectionStatisticList = async () => {
  loading.value = true
  try {
    const params: SectionStatisticQueryParams = {}

    // 构建查询参数
    if (formData.value.timeRange[0]) {
      params.startTime = formatTime(formData.value.timeRange[0])
    }
    if (formData.value.timeRange[1]) {
      params.endTime = formatTime(formData.value.timeRange[1])
    }
    if (formData.value.name) {
      params.name = formData.value.name
    }
    if (formData.value.volt) {
      params.volt = formData.value.volt
    }
    if (formData.value.status) {
      params.status = formData.value.status
    }

    const response = await SectionMonitoringService.getSectionStatisticList(params)
    console.log('🚀 ~ fetchSectionStatisticList ~ response:', response)

    // 响应拦截器已经处理了数据格式，response直接就是数据数组
    if (response && Array.isArray(response)) {
      const mappedData = response.map((item: SectionStatisticData) => ({
        id: item.id,
        name: item.name,
        volt: item.volt,
        limit: item.limit,
        maxValue: item.maxValue,
        maxDiffValue: item.maxDiffValue,
        totalOverTime: item.totalOverTime,
        longestOverTime: item.longestOverTime,
        maxValueTime: item.maxValueTime,
      }))

      // 应用默认排序
      tableData.value = applyDefaultSort(mappedData)
    }
  } catch (error) {
    console.error('获取断面统计数据失败:', error)
    // 这里可以添加错误提示
  } finally {
    loading.value = false
  }
}

// 搜索处理函数
const handleSearch = () => {
  fetchSectionStatisticList()
}

// 重置处理函数
const handleReset = () => {
  // 重置表单数据
  formData.value = {
    timeRange: [timestampRange[0], timestampRange[1]],
    name: '',
    volt: null,
    status: null,
  }
  // 重新获取数据
  fetchSectionStatisticList()
}

// 排序处理函数
const handleSort = (column: any, order: 'asc' | 'desc' | null, multiSortState: any) => {
  console.log('排序:', column.key, order, multiSortState)

  if (!order && Object.keys(multiSortState).length === 0) {
    // 恢复原始顺序
    fetchSectionStatisticList()
    return
  }

  // 如果有多列排序状态，使用多列排序
  if (Object.keys(multiSortState).length > 0) {
    // 按优先级排序多列排序状态
    const sortColumns = Object.entries(multiSortState)
      .map(([key, state]: [string, any]) => ({
        key,
        order: state.order,
        multiple: state.multiple,
        column: columns.find((col: any) => col.key === key),
      }))
      .sort((a, b) => b.multiple - a.multiple) // 按优先级降序排列

    tableData.value.sort((a, b) => {
      for (const sortCol of sortColumns) {
        let result = 0

        // 如果列有自定义比较函数，使用它
        if (
          sortCol.column?.sorter &&
          typeof sortCol.column.sorter === 'object' &&
          sortCol.column.sorter.compare
        ) {
          result = sortCol.column.sorter.compare(a, b)
        } else {
          // 默认排序逻辑
          const aValue = (a as any)[sortCol.key]
          const bValue = (b as any)[sortCol.key]

          // 处理数值类型的列
          if (
            ['limit', 'maxValue', 'maxDiffValue', 'totalOverTime', 'longestOverTime'].includes(
              sortCol.key,
            )
          ) {
            const aNum = parseFloat(aValue) || 0
            const bNum = parseFloat(bValue) || 0
            result = aNum - bNum
          } else {
            // 处理字符串类型的列
            const aStr = String(aValue || '')
            const bStr = String(bValue || '')
            result = aStr.localeCompare(bStr, 'zh-CN', { numeric: true })
          }
        }

        // 根据排序方向调整结果
        if (sortCol.order === 'desc') {
          result = -result
        }

        // 如果当前列的比较结果不为0，返回结果
        if (result !== 0) {
          return result
        }
      }

      return 0 // 所有列都相等
    })
  } else {
    // 单列排序（保持向后兼容）
    tableData.value.sort((a, b) => {
      const aValue = (a as any)[column.key]
      const bValue = (b as any)[column.key]

      // 处理数值类型的列
      if (
        ['limit', 'maxValue', 'maxDiffValue', 'totalOverTime', 'longestOverTime'].includes(
          column.key,
        )
      ) {
        const aNum = parseFloat(aValue) || 0
        const bNum = parseFloat(bValue) || 0

        if (order === 'asc') {
          return aNum - bNum
        } else {
          return bNum - aNum
        }
      }

      // 处理字符串类型的列
      const aStr = String(aValue || '')
      const bStr = String(bValue || '')

      if (order === 'asc') {
        return aStr.localeCompare(bStr, 'zh-CN', { numeric: true })
      } else {
        return bStr.localeCompare(aStr, 'zh-CN', { numeric: true })
      }
    })
  }
}

// 查看详情处理函数
const handleViewDetail = (item: TableRowData, index: number) => {
  console.log('查看详情:', item, index)
  sectionMonitoringStore.selectedTableRow = item
  // 这里可以添加查看详情的逻辑，比如打开详情弹窗或跳转到详情页面
}

// 去除详情列
const columnsByExport = computed(() => {
  return columns.filter((item: any) => item.key !== 'action')
})

// 导出表格处理函数
const handleExport = (data: any[], columns: any[], filename: string, exportType: string) => {
  console.log('导出表格:', { data, columns, filename, exportType })
  // 导出逻辑已在ExportButton组件中实现
}

// 组件挂载时获取数据
onMounted(() => {
  fetchSectionStatisticList()
})
</script>
<style></style>
